import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { ThemeProvider } from 'next-themes'
import { Event, Member } from '@/types'

// Custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <ThemeProvider attribute="class" defaultTheme="light" enableSystem={false}>
      {children}
    </ThemeProvider>
  )
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

export * from '@testing-library/react'
export { customRender as render }

// Mock data for testing
export const mockEvent: Event = {
  id: '1',
  title: 'React Workshop',
  description: 'Learn React fundamentals and advanced concepts',
  category: 'Technical',
  date: '2024-02-15',
  time: '10:00 AM - 12:00 PM',
  duration: '2 hours',
  location: 'Room 101',
  speaker: '<PERSON>',
  prerequisites: 'Basic JavaScript knowledge',
  registrationLink: 'https://example.com/register',
  posterUrl: 'https://example.com/poster.jpg',
}

export const mockEvents: Event[] = [
  mockEvent,
  {
    id: '2',
    title: 'Python Bootcamp',
    description: 'Comprehensive Python programming course',
    category: 'Technical',
    date: '2024-02-20',
    time: '2:00 PM - 5:00 PM',
    duration: '3 hours',
    location: 'Lab 2',
    speaker: 'Jane Smith',
    prerequisites: 'None',
    registrationLink: 'https://example.com/register-python',
  },
  {
    id: '3',
    title: 'Networking Event',
    description: 'Meet fellow developers and industry professionals',
    category: 'Non-Technical',
    date: '2024-01-10', // Past event
    time: '6:00 PM - 8:00 PM',
    location: 'Auditorium',
  },
]

export const mockMember: Member = {
  name: 'Alice Johnson',
  role: 'President',
  photo: 'https://example.com/alice.jpg',
  quote: 'Passionate about technology and community building',
  linkedin: 'https://linkedin.com/in/alice',
  instagram: 'https://instagram.com/alice',
}

export const mockMembers: Member[] = [
  mockMember,
  {
    name: 'Bob Wilson',
    role: 'Vice President',
    photo: 'https://example.com/bob.jpg',
    quote: 'Dedicated to fostering learning and growth',
    linkedin: 'https://linkedin.com/in/bob',
    instagram: 'https://instagram.com/bob',
  },
]

// Helper functions for testing
export const createMockFetch = (data: any, ok: boolean = true) => {
  return jest.fn().mockResolvedValue({
    ok,
    json: jest.fn().mockResolvedValue(data),
  })
}

export const createMockFetchError = (error: string) => {
  return jest.fn().mockRejectedValue(new Error(error))
}

// Accessibility testing helpers
export const checkAccessibility = async (container: HTMLElement) => {
  // Check for basic accessibility requirements
  const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6')
  const images = container.querySelectorAll('img')
  const buttons = container.querySelectorAll('button')
  const links = container.querySelectorAll('a')

  // Check that images have alt text
  images.forEach((img) => {
    expect(img).toHaveAttribute('alt')
  })

  // Check that buttons have accessible names
  buttons.forEach((button) => {
    const hasAccessibleName = 
      button.textContent ||
      button.getAttribute('aria-label') ||
      button.getAttribute('aria-labelledby')
    expect(hasAccessibleName).toBeTruthy()
  })

  // Check that links have accessible names
  links.forEach((link) => {
    const hasAccessibleName = 
      link.textContent ||
      link.getAttribute('aria-label') ||
      link.getAttribute('aria-labelledby')
    expect(hasAccessibleName).toBeTruthy()
  })

  return true
}

// Performance testing helpers
export const measureRenderTime = (renderFn: () => void) => {
  const start = performance.now()
  renderFn()
  const end = performance.now()
  return end - start
}

// Mock intersection observer for lazy loading tests
export const mockIntersectionObserver = (isIntersecting: boolean = true) => {
  const mockObserver = jest.fn()
  mockObserver.mockReturnValue({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  })

  window.IntersectionObserver = mockObserver

  // Trigger intersection
  const callback = mockObserver.mock.calls[0]?.[0]
  if (callback) {
    callback([{ isIntersecting }])
  }

  return mockObserver
}

// Theme testing helpers
export const renderWithTheme = (ui: ReactElement, theme: 'light' | 'dark' = 'light') => {
  const ThemeWrapper = ({ children }: { children: React.ReactNode }) => (
    <ThemeProvider attribute="class" defaultTheme={theme} enableSystem={false}>
      {children}
    </ThemeProvider>
  )

  return render(ui, { wrapper: ThemeWrapper })
}

// Error boundary testing
export const ThrowError = ({ shouldThrow }: { shouldThrow: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error')
  }
  return <div>No error</div>
}
