Stack trace:
Frame         Function      Args
0007FFFF8E80  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF8E80, 0007FFFF7D80) msys-2.0.dll+0x1FE8E
0007FFFF8E80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9158) msys-2.0.dll+0x67F9
0007FFFF8E80  000210046832 (000210286019, 0007FFFF8D38, 0007FFFF8E80, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF8E80  000210068E24 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9160  00021006A225 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA29BE0000 ntdll.dll
7FFA27E70000 KERNEL32.DLL
7FFA26D10000 KERNELBASE.dll
7FFA28B30000 USER32.dll
7FFA27940000 win32u.dll
7FFA292F0000 GDI32.dll
7FFA273A0000 gdi32full.dll
7FFA27110000 msvcp_win.dll
7FFA271C0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA29AA0000 advapi32.dll
7FFA28D00000 msvcrt.dll
7FFA27A30000 sechost.dll
7FFA27F40000 RPCRT4.dll
7FFA26300000 CRYPTBASE.DLL
7FFA27720000 bcryptPrimitives.dll
7FFA299E0000 IMM32.DLL
