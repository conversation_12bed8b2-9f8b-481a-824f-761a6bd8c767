import { render, screen, fireEvent, waitFor, checkAccessibility } from '../utils/test-utils'
import userEvent from '@testing-library/user-event'
import { Navbar } from '@/components/navbar'

// Mock next-themes
jest.mock('next-themes', () => ({
  useTheme: () => ({
    theme: 'light',
    setTheme: jest.fn(),
  }),
}))

describe('Navbar', () => {
  beforeEach(() => {
    // Reset any mocks
    jest.clearAllMocks()
  })

  it('renders the navbar with logo and navigation links', () => {
    render(<Navbar />)
    
    // Check logo
    expect(screen.getByAltText('GeeksforGeeks Student Chapter')).toBeInTheDocument()
    
    // Check navigation links (desktop)
    expect(screen.getByRole('link', { name: /events/i })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: /about/i })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: /learning/i })).toBeInTheDocument()
    
    // Check POTD link
    expect(screen.getByRole('link', { name: /gfg potd/i })).toBeInTheDocument()
  })

  it('has proper accessibility attributes', () => {
    render(<Navbar />)
    
    // Check main navigation landmark
    const nav = screen.getByRole('navigation', { name: /main navigation/i })
    expect(nav).toBeInTheDocument()
    expect(nav).toHaveAttribute('id', 'main-navigation')
    
    // Check skip links
    expect(screen.getByRole('link', { name: /skip to main content/i })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: /skip to navigation/i })).toBeInTheDocument()
  })

  it('toggles mobile menu when hamburger button is clicked', async () => {
    const user = userEvent.setup()
    render(<Navbar />)
    
    // Find mobile menu button
    const menuButton = screen.getByRole('button', { name: /open menu/i })
    expect(menuButton).toBeInTheDocument()
    
    // Initially menu should be closed
    expect(screen.getByRole('button', { name: /open menu/i })).toBeInTheDocument()
    
    // Click to open menu
    await user.click(menuButton)
    
    // Menu should now be open
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /close menu/i })).toBeInTheDocument()
    })
    
    // Mobile menu should be visible
    const mobileMenu = screen.getByRole('menu')
    expect(mobileMenu).toBeInTheDocument()
  })

  it('closes mobile menu when a navigation link is clicked', async () => {
    const user = userEvent.setup()
    render(<Navbar />)
    
    // Open mobile menu
    const menuButton = screen.getByRole('button', { name: /open menu/i })
    await user.click(menuButton)
    
    // Wait for menu to open
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /close menu/i })).toBeInTheDocument()
    })
    
    // Click on a navigation link in mobile menu
    const eventsLink = screen.getAllByRole('menuitem', { name: /events/i })[0]
    await user.click(eventsLink)
    
    // Menu should close (button text changes back)
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /open menu/i })).toBeInTheDocument()
    })
  })

  it('has proper keyboard navigation support', async () => {
    const user = userEvent.setup()
    render(<Navbar />)
    
    // Tab through navigation elements
    await user.tab()
    expect(screen.getByRole('link', { name: /skip to main content/i })).toHaveFocus()
    
    await user.tab()
    expect(screen.getByRole('link', { name: /skip to navigation/i })).toHaveFocus()
    
    await user.tab()
    expect(screen.getByRole('link', { name: /geeksforgeeks student chapter - home/i })).toHaveFocus()
  })

  it('indicates current page in navigation', () => {
    // Mock usePathname to return /events
    jest.doMock('next/navigation', () => ({
      usePathname: () => '/events',
      useRouter: () => ({
        push: jest.fn(),
        replace: jest.fn(),
        prefetch: jest.fn(),
      }),
    }))
    
    render(<Navbar />)
    
    // Events link should have aria-current="page"
    const eventsLink = screen.getByRole('link', { name: /events.*current page/i })
    expect(eventsLink).toHaveAttribute('aria-current', 'page')
  })

  it('has proper ARIA attributes for mobile menu', async () => {
    const user = userEvent.setup()
    render(<Navbar />)
    
    const menuButton = screen.getByRole('button', { name: /open menu/i })
    
    // Check initial ARIA attributes
    expect(menuButton).toHaveAttribute('aria-expanded', 'false')
    expect(menuButton).toHaveAttribute('aria-controls', 'mobile-menu')
    
    // Open menu
    await user.click(menuButton)
    
    // Check updated ARIA attributes
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /close menu/i })).toHaveAttribute('aria-expanded', 'true')
    })
    
    // Check mobile menu attributes
    const mobileMenu = screen.getByRole('menu')
    expect(mobileMenu).toHaveAttribute('id', 'mobile-menu')
  })

  it('handles theme toggle', async () => {
    const mockSetTheme = jest.fn()
    
    // Mock useTheme with setTheme function
    jest.doMock('next-themes', () => ({
      useTheme: () => ({
        theme: 'light',
        setTheme: mockSetTheme,
      }),
    }))
    
    const user = userEvent.setup()
    render(<Navbar />)
    
    // Find theme toggle button
    const themeButton = screen.getByRole('button', { name: /toggle theme/i })
    expect(themeButton).toBeInTheDocument()
    
    // Click theme toggle
    await user.click(themeButton)
    
    // setTheme should be called
    expect(mockSetTheme).toHaveBeenCalledWith('dark')
  })

  it('has proper external link attributes', () => {
    render(<Navbar />)
    
    const potdLink = screen.getByRole('link', { name: /gfg potd.*opens in new tab/i })
    expect(potdLink).toHaveAttribute('target', '_blank')
    expect(potdLink).toHaveAttribute('rel', 'noopener noreferrer')
  })

  it('supports keyboard navigation with Enter and Space keys', async () => {
    const user = userEvent.setup()
    render(<Navbar />)
    
    const menuButton = screen.getByRole('button', { name: /open menu/i })
    
    // Focus the menu button
    menuButton.focus()
    expect(menuButton).toHaveFocus()
    
    // Press Enter to open menu
    await user.keyboard('{Enter}')
    
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /close menu/i })).toBeInTheDocument()
    })
  })

  it('meets accessibility standards', async () => {
    const { container } = render(<Navbar />)
    await checkAccessibility(container)
  })
})
