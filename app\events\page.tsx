"use client"

import { useEffect, useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Calendar, Clock, User, ChevronDown, ChevronUp, ChevronLeft, ChevronRight, Trophy, Target, Users, Code } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Event, EventCardProps } from "@/types"
import { ApiErrorBoundary } from "@/components/api-error-boundary"

export default function EventsPage() {
  const [events, setEvents] = useState<Event[]>([])
  const [expandedEvent, setExpandedEvent] = useState<string | null>(null)
  const [currentUpcomingEventIndex, setCurrentUpcomingEventIndex] = useState(0)
  const [fetchError, setFetchError] = useState<Error | null>(null)

  const fetchEvents = async () => {
    try {
      setFetchError(null)

      // Check if API base URL is configured
      if (!process.env.NEXT_PUBLIC_API_BASE) {
        console.warn("API base URL not configured, using mock data")
        setEvents(mockEvents)
        return
      }

      const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE}/events`)
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: Failed to fetch events`)
      }
      const data = await res.json()
      setEvents(Array.isArray(data) ? data : [])
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch events')
      console.warn("Failed to fetch events from API, using mock data:", error)
      // Use mock data as fallback instead of showing error
      setEvents(mockEvents)
      setFetchError(null)
    }
  }

  useEffect(() => {
    fetchEvents()
  }, [])

  const toggleEvent = (eventId: string) => {
    setExpandedEvent(expandedEvent === eventId ? null : eventId)
  }

  const parseDate = (dateStr: string) => {
    const [start, end] = dateStr.split(" to ");
    return {
      startDate: new Date(start.trim()),
      endDate: new Date((end || start).trim())
    }
  }
  
  const upcomingEvents = events
    .filter(event => parseDate(event.date).endDate >= new Date())
    .sort((a, b) => parseDate(a.date).startDate.getTime() - parseDate(b.date).startDate.getTime());
  
  const pastEvents = events
    .filter(event => parseDate(event.date).endDate < new Date())
    .sort((a, b) => parseDate(b.date).endDate.getTime() - parseDate(a.date).endDate.getTime());
  

  const nextUpcomingEvent = () => {
    setCurrentUpcomingEventIndex((prevIndex) =>
      (prevIndex + 1) % upcomingEvents.length
    )
  }

  const prevUpcomingEvent = () => {
    setCurrentUpcomingEventIndex((prevIndex) =>
      (prevIndex - 1 + upcomingEvents.length) % upcomingEvents.length
    )
  }

  const EventCard = ({ event, isPast = false }: EventCardProps) => (
    <Card className="w-full overflow-hidden transition-shadow duration-300 mb-8 border border-gray-200 rounded-xl hover:shadow-xl hover:border-green-700 bg-white">
      {event.posterUrl && (
        <img
          src={event.posterUrl}
          alt={event.title}
          className="w-full h-[500px] object-cover object-top border-b border-gray-200"
        />
      )}
      <CardHeader className="bg-green-700 text-white p-4">
        <CardTitle className="flex items-center justify-between">
          <span className="text-2xl font-semibold">{event.title}</span>
          <Badge variant={isPast ? "secondary" : "default"}>{event.category}</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6 space-y-4">
        <p className="text-muted-foreground text-lg">{event.description}</p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2 text-sm text-green-800">
            <div className="flex items-center">
              <Calendar className="mr-2 h-4 w-4" />
              <time>{event.date}</time>
            </div>
            <div className="flex items-center">
              <Clock className="mr-2 h-4 w-4" />
              <span>{event.time}</span>
            </div>
          </div>
          <div className="space-y-2 text-sm text-green-800">
            {event.speakers && (
              <div className="flex items-center">
                <User className="mr-2 h-4 w-4" />
                <span>{event.speakers}</span>
              </div>
            )}
          </div>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => toggleEvent(event.id)}
          className="w-full mt-4 border-green-700 text-green-700 hover:bg-green-100"
        >
          {expandedEvent === event.id ? (
            <>
              Less Info <ChevronUp className="ml-2 h-4 w-4" />
            </>
          ) : (
            <>
              More Info <ChevronDown className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>

        <AnimatePresence>
          {expandedEvent === event.id && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="space-y-4 mt-4"
            >
              <div className="rounded-lg bg-green-50 p-4">
                <h3 className="font-semibold mb-2 text-green-700">Event Details</h3>
                <ul className="list-disc list-inside space-y-2 text-sm text-muted-foreground">
                  <li>Registration deadline: 2 days before event</li>
                  <li>Prerequisites: {event.prerequisites || 'None'}</li>
                  <li>Certificate will be provided</li>
                  <li>Limited seats available</li>
                </ul>
              </div>
              {!isPast && event.registrationLink ? (
  <a
    href={event.registrationLink}
    target="_blank"
    rel="noopener noreferrer"
    className="block"
  >
    <Button className="w-full bg-green-700 text-white hover:bg-green-800 text-lg font-bold">
      Register Now
    </Button>
  </a>
) : (
  <Button
    disabled
    className="w-full bg-gray-300 text-gray-700 cursor-not-allowed text-lg font-semibold"
  >
    Registrations Closed
  </Button>
)}

            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>
    </Card>
  )

  // Handle fetch errors
  if (fetchError) {
    return (
      <main className="container py-12 mt-20 px-6 mx-auto">
        <ApiErrorBoundary onRetry={fetchEvents}>
          <div>Error loading events</div>
        </ApiErrorBoundary>
      </main>
    )
  }

  return (
    <main className="container py-12 mt-20 px-6 mx-auto">
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="mb-16 text-center"
      >
        <h1 className="mb-4 text-4xl font-bold">Events</h1>
        <p className="mx-auto max-w-2xl text-lg text-muted-foreground">
          Discover upcoming technical events, workshops, and competitions organized by GeeksforGeeks Student Chapter at MIT-ADT University.
        </p>
      </motion.section>

      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.2 }}
        className="mb-16"
      >
        <h2 className="mb-8 text-2xl font-bold">Why Attend Our Events?</h2>
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
          {eventBenefits.map((benefit, index) => (
            <motion.div
              key={benefit.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              className="flex flex-col items-center"
            >
              <Card>
                <CardContent className="flex items-start gap-4 p-6">
                  <benefit.icon className="h-6 w-6 text-primary" />
                  <div>
                    <h3 className="mb-2 font-semibold">{benefit.title}</h3>
                    <p className="text-muted-foreground">{benefit.description}</p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.section>

      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.4 }}
        className="mb-16"
      >
        <h2 className="mb-8 text-2xl font-bold">Event Statistics</h2>
        <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-4">
          {eventStats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
            >
              <Card>
                <CardContent className="flex flex-col items-center p-6 text-center">
                  <div className="text-3xl font-bold text-primary">{stat.value}</div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.section>

      <section className="mb-16">
        <h2 className="mb-8 text-2xl font-bold">Our Events</h2>
        <Tabs defaultValue="upcoming" className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-8">
            <TabsTrigger value="upcoming">Upcoming Events</TabsTrigger>
            <TabsTrigger value="past">Past Events</TabsTrigger>
          </TabsList>

          <TabsContent value="upcoming">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {upcomingEvents.length > 0 ? (
                <div className="relative">
                  <EventCard event={upcomingEvents[currentUpcomingEventIndex]} />
                  {upcomingEvents.length > 1 && (
                    <div className="absolute top-1/2 -mt-4 w-full flex justify-between">
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={prevUpcomingEvent}
                        className="rounded-full border-green-500 hover:bg-green-100"
                      >
                        <ChevronLeft className="h-4 w-4 text-green-500" />
                      </Button>
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={nextUpcomingEvent}
                        className="rounded-full border-green-500 hover:bg-green-100"
                      >
                        <ChevronRight className="h-4 w-4 text-green-500" />
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-center text-muted-foreground">No upcoming events at the moment. Check back soon!</p>
              )}
            </motion.div>
          </TabsContent>

          <TabsContent value="past">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
            >
              {pastEvents.map(event => (
                <EventCard key={event.id} event={event} isPast={true} />
              ))}
            </motion.div>
          </TabsContent>
        </Tabs>
      </section>
    </main>
  )
}

const eventBenefits = [
  {
    title: "Skill Development",
    description: "Enhance your technical skills through hands-on workshops and coding competitions.",
    icon: Code,
  },
  {
    title: "Networking",
    description: "Connect with like-minded peers, industry professionals, and potential mentors.",
    icon: Users,
  },
  {
    title: "Recognition",
    description: "Gain recognition for your achievements and build your professional portfolio.",
    icon: Trophy,
  },
  {
    title: "Career Growth",
    description: "Access career opportunities, internships, and industry insights.",
    icon: Target,
  },
]

const eventStats = [
  {
    value: "25+",
    label: "Events Organized",
  },
  {
    value: "500+",
    label: "Participants",
  },
  {
    value: "50+",
    label: "Workshops Conducted",
  },
  {
    value: "100%",
    label: "Satisfaction Rate",
  },
]

const mockEvents: Event[] = [
  {
    id: "1",
    title: "AI Odyssey: Machine Learning Workshop",
    description: "Dive deep into the world of artificial intelligence and machine learning. Learn about neural networks, data preprocessing, and model training through hands-on exercises.",
    date: "2024-12-15 to 2024-12-16",
    time: "10:00 AM - 4:00 PM",
    category: "Technical",
    speakers: "Dr. Priya Sharma, ML Engineer at Google",
    posterUrl: "/events/ai-odyssey.jpg",
    registrationLink: "https://forms.google.com/ai-odyssey",
    prerequisites: "Basic Python knowledge"
  },
  {
    id: "2",
    title: "CodeCraft: Competitive Programming Bootcamp",
    description: "Master the art of competitive programming with advanced algorithms and data structures. Perfect for students preparing for coding interviews and contests.",
    date: "2024-12-20 to 2024-12-22",
    time: "9:00 AM - 5:00 PM",
    category: "Technical",
    speakers: "Rahul Verma, Software Engineer at Microsoft",
    posterUrl: "/events/codecraft.jpg",
    registrationLink: "https://forms.google.com/codecraft",
    prerequisites: "Intermediate programming skills"
  },
  {
    id: "3",
    title: "Web Development Masterclass",
    description: "Learn modern web development with React, Node.js, and MongoDB. Build full-stack applications from scratch.",
    date: "2024-11-15 to 2024-11-16",
    time: "10:00 AM - 6:00 PM",
    category: "Technical",
    speakers: "Anita Desai, Full Stack Developer",
    posterUrl: "/events/web-dev.jpg",
    registrationLink: undefined,
    prerequisites: "HTML, CSS, JavaScript basics"
  },
  {
    id: "4",
    title: "Cybersecurity Awareness Session",
    description: "Understanding modern cybersecurity threats and how to protect yourself and your applications from common attacks.",
    date: "2024-10-25",
    time: "2:00 PM - 4:00 PM",
    category: "Technical",
    speakers: "Vikram Singh, Cybersecurity Expert",
    posterUrl: "/events/cybersecurity.jpg",
    registrationLink: undefined,
    prerequisites: "None"
  }
]
