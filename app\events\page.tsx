"use client"

import { useEffect, useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Calendar, Clock, User, ChevronDown, ChevronUp, ChevronLeft, ChevronRight, Trophy, Target, Users, Code } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Event, EventCardProps } from "@/types"
import { ApiErrorBoundary } from "@/components/api-error-boundary"

export default function EventsPage() {
  const [events, setEvents] = useState<Event[]>([])
  const [expandedEvent, setExpandedEvent] = useState<string | null>(null)
  const [currentUpcomingEventIndex, setCurrentUpcomingEventIndex] = useState(0)
  const [fetchError, setFetchError] = useState<Error | null>(null)

  const fetchEvents = async () => {
    try {
      setFetchError(null)
      const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE}/events`)
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: Failed to fetch events`)
      }
      const data = await res.json()
      setEvents(Array.isArray(data) ? data : [])
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch events')
      console.error("Failed to fetch events:", error)
      setFetchError(error)
      setEvents([])
    }
  }

  useEffect(() => {
    fetchEvents()
  }, [])

  const toggleEvent = (eventId: string) => {
    setExpandedEvent(expandedEvent === eventId ? null : eventId)
  }

  const parseDate = (dateStr: string) => {
    const [start, end] = dateStr.split(" to ");
    return {
      startDate: new Date(start.trim()),
      endDate: new Date((end || start).trim())
    }
  }
  
  const upcomingEvents = events
    .filter(event => parseDate(event.date).endDate >= new Date())
    .sort((a, b) => parseDate(a.date).startDate.getTime() - parseDate(b.date).startDate.getTime());
  
  const pastEvents = events
    .filter(event => parseDate(event.date).endDate < new Date())
    .sort((a, b) => parseDate(b.date).endDate.getTime() - parseDate(a.date).endDate.getTime());
  

  const nextUpcomingEvent = () => {
    setCurrentUpcomingEventIndex((prevIndex) =>
      (prevIndex + 1) % upcomingEvents.length
    )
  }

  const prevUpcomingEvent = () => {
    setCurrentUpcomingEventIndex((prevIndex) =>
      (prevIndex - 1 + upcomingEvents.length) % upcomingEvents.length
    )
  }

  const EventCard = ({ event, isPast = false }: EventCardProps) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={{ y: -5 }}
      className="group"
    >
      <Card className="w-full overflow-hidden transition-all duration-500 mb-8 border border-gray-200/50 rounded-2xl hover:shadow-2xl hover:border-green-500/50 bg-white/80 backdrop-blur-sm hover:bg-white/90">
        {event.posterUrl && (
          <div className="relative overflow-hidden">
            <img
              src={event.posterUrl}
              alt={event.title}
              className="w-full h-[400px] object-cover object-center transition-transform duration-500 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          </div>
        )}

        <div className="relative">
          <CardHeader className="bg-gradient-to-r from-green-600 to-green-700 text-white p-6 relative overflow-hidden">
            <div className="absolute inset-0 bg-white/10 backdrop-blur-sm" />
            <CardTitle className="relative flex items-center justify-between">
              <motion.span
                className="text-2xl font-bold"
                initial={{ x: -20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                {event.title}
              </motion.span>
              <Badge
                variant={isPast ? "secondary" : "default"}
                className={`${isPast ? 'bg-gray-500/80' : 'bg-green-500/80'} backdrop-blur-sm text-white border-0 px-3 py-1 text-sm font-medium`}
              >
                {event.category}
              </Badge>
            </CardTitle>
          </CardHeader>

          <CardContent className="p-6 space-y-6 bg-white/60 backdrop-blur-sm">
            <p className="text-gray-700 text-lg leading-relaxed">{event.description}</p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <div className="flex items-center p-3 rounded-lg bg-green-50/80 backdrop-blur-sm border border-green-100">
                  <Calendar className="mr-3 h-5 w-5 text-green-600" />
                  <div>
                    <p className="text-sm font-medium text-green-800">Date</p>
                    <time className="text-sm text-green-700">{event.date}</time>
                  </div>
                </div>
                <div className="flex items-center p-3 rounded-lg bg-blue-50/80 backdrop-blur-sm border border-blue-100">
                  <Clock className="mr-3 h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium text-blue-800">Time</p>
                    <span className="text-sm text-blue-700">{event.time}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                {event.speakers && (
                  <div className="flex items-center p-3 rounded-lg bg-purple-50/80 backdrop-blur-sm border border-purple-100">
                    <User className="mr-3 h-5 w-5 text-purple-600" />
                    <div>
                      <p className="text-sm font-medium text-purple-800">Speaker</p>
                      <span className="text-sm text-purple-700">{event.speakers}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <Button
              variant="outline"
              size="lg"
              onClick={() => toggleEvent(event.id)}
              className="w-full mt-6 border-green-600/50 text-green-700 hover:bg-green-50/80 backdrop-blur-sm transition-all duration-300 font-medium py-3 rounded-xl"
            >
              {expandedEvent === event.id ? (
                <>
                  Less Info <ChevronUp className="ml-2 h-5 w-5" />
                </>
              ) : (
                <>
                  More Info <ChevronDown className="ml-2 h-5 w-5" />
                </>
              )}
            </Button>

            <AnimatePresence>
              {expandedEvent === event.id && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.4, ease: "easeInOut" }}
                  className="space-y-6 mt-6"
                >
                  <div className="rounded-xl bg-gradient-to-br from-green-50/80 to-green-100/60 backdrop-blur-sm p-6 border border-green-200/50">
                    <h3 className="font-bold mb-4 text-green-800 text-xl">Event Details</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <ul className="space-y-3 text-green-700">
                        <li className="flex items-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                          <span className="text-base">Registration deadline: 2 days before event</span>
                        </li>
                        <li className="flex items-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                          <span className="text-base">Prerequisites: {event.prerequisites || 'None'}</span>
                        </li>
                      </ul>
                      <ul className="space-y-3 text-green-700">
                        <li className="flex items-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                          <span className="text-base">Certificate will be provided</span>
                        </li>
                        <li className="flex items-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                          <span className="text-base">Limited seats available</span>
                        </li>
                      </ul>
                    </div>
                  </div>

                  {!isPast && event.registrationLink ? (
                    <motion.a
                      href={event.registrationLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Button className="w-full bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 text-xl font-bold py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                        Register Now
                      </Button>
                    </motion.a>
                  ) : (
                    <Button
                      disabled
                      className="w-full bg-gray-300/80 text-gray-600 cursor-not-allowed text-xl font-semibold py-4 rounded-xl backdrop-blur-sm"
                    >
                      {isPast ? 'Event Completed' : 'Registrations Closed'}
                    </Button>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </CardContent>
        </div>
      </Card>
    </motion.div>
  )

  // Handle fetch errors
  if (fetchError) {
    return (
      <main className="container py-12 mt-20 px-6 mx-auto">
        <ApiErrorBoundary onRetry={fetchEvents}>
          <div>Error loading events</div>
        </ApiErrorBoundary>
      </main>
    )
  }

  return (
    <main className="container py-12 mt-20 px-6 mx-auto">
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="mb-16 text-center"
      >
        <h1 className="mb-4 text-4xl font-bold">Events</h1>
        <p className="mx-auto max-w-2xl text-lg text-muted-foreground">
          Discover upcoming technical events, workshops, and competitions organized by GeeksforGeeks Student Chapter at MIT-ADT University.
        </p>
      </motion.section>

      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.2 }}
        className="mb-16"
      >
        <h2 className="mb-8 text-2xl font-bold">Why Attend Our Events?</h2>
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
          {eventBenefits.map((benefit, index) => (
            <motion.div
              key={benefit.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              className="flex flex-col items-center"
            >
              <Card>
                <CardContent className="flex items-start gap-4 p-6">
                  <benefit.icon className="h-6 w-6 text-primary" />
                  <div>
                    <h3 className="mb-2 font-semibold">{benefit.title}</h3>
                    <p className="text-muted-foreground">{benefit.description}</p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.section>

      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.4 }}
        className="mb-16"
      >
        <h2 className="mb-8 text-2xl font-bold">Event Statistics</h2>
        <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-4">
          {eventStats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
            >
              <Card>
                <CardContent className="flex flex-col items-center p-6 text-center">
                  <div className="text-3xl font-bold text-primary">{stat.value}</div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.section>

      <section className="mb-16">
        <h2 className="mb-8 text-2xl font-bold">Our Events</h2>
        <Tabs defaultValue="upcoming" className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-8">
            <TabsTrigger value="upcoming">Upcoming Events</TabsTrigger>
            <TabsTrigger value="past">Past Events</TabsTrigger>
          </TabsList>

          <TabsContent value="upcoming">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {upcomingEvents.length > 0 ? (
                <div className="relative">
                  <EventCard event={upcomingEvents[currentUpcomingEventIndex]} />
                  {upcomingEvents.length > 1 && (
                    <div className="absolute top-1/2 -mt-4 w-full flex justify-between">
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={prevUpcomingEvent}
                        className="rounded-full border-green-500 hover:bg-green-100"
                      >
                        <ChevronLeft className="h-4 w-4 text-green-500" />
                      </Button>
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={nextUpcomingEvent}
                        className="rounded-full border-green-500 hover:bg-green-100"
                      >
                        <ChevronRight className="h-4 w-4 text-green-500" />
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-center text-muted-foreground">No upcoming events at the moment. Check back soon!</p>
              )}
            </motion.div>
          </TabsContent>

          <TabsContent value="past">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
            >
              {pastEvents.map(event => (
                <EventCard key={event.id} event={event} isPast={true} />
              ))}
            </motion.div>
          </TabsContent>
        </Tabs>
      </section>
    </main>
  )
}

const eventBenefits = [
  {
    title: "Skill Development",
    description: "Enhance your technical skills through hands-on workshops and coding competitions.",
    icon: Code,
  },
  {
    title: "Networking",
    description: "Connect with like-minded peers, industry professionals, and potential mentors.",
    icon: Users,
  },
  {
    title: "Recognition",
    description: "Gain recognition for your achievements and build your professional portfolio.",
    icon: Trophy,
  },
  {
    title: "Career Growth",
    description: "Access career opportunities, internships, and industry insights.",
    icon: Target,
  },
]

const eventStats = [
  {
    value: "25+",
    label: "Events Organized",
  },
  {
    value: "500+",
    label: "Participants",
  },
  {
    value: "50+",
    label: "Workshops Conducted",
  },
  {
    value: "100%",
    label: "Satisfaction Rate",
  },
]


