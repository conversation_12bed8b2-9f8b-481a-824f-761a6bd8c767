import { render, screen, waitFor } from '../utils/test-utils'
import { createMockFetch, createMockFetchError, mockEvents } from '../utils/test-utils'
import EventsPage from '@/app/events/page'

describe('Events Page Integration', () => {
  beforeEach(() => {
    // Reset fetch mock
    global.fetch = jest.fn()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  it('renders events page with loading state initially', () => {
    global.fetch = createMockFetch(mockEvents)
    
    render(<EventsPage />)

    // Should show the page title
    expect(screen.getByRole('heading', { name: /upcoming events/i })).toBeInTheDocument()
  })

  it('displays events after successful API call', async () => {
    global.fetch = createMockFetch(mockEvents)
    
    render(<EventsPage />)

    // Wait for events to load
    await waitFor(() => {
      expect(screen.getByText('React Workshop')).toBeInTheDocument()
    })

    expect(screen.getByText('Python Bootcamp')).toBeInTheDocument()
    expect(screen.getByText('Learn React fundamentals and advanced concepts')).toBeInTheDocument()
  })

  it('separates upcoming and past events correctly', async () => {
    global.fetch = createMockFetch(mockEvents)
    
    render(<EventsPage />)

    await waitFor(() => {
      expect(screen.getByText('React Workshop')).toBeInTheDocument()
    })

    // Check that upcoming events are in the upcoming section
    const upcomingSection = screen.getByRole('heading', { name: /upcoming events/i }).closest('section')
    expect(upcomingSection).toContainElement(screen.getByText('React Workshop'))
    expect(upcomingSection).toContainElement(screen.getByText('Python Bootcamp'))

    // Check that past events are in the past section
    const pastSection = screen.getByRole('heading', { name: /past events/i }).closest('section')
    expect(pastSection).toContainElement(screen.getByText('Networking Event'))
  })

  it('handles API errors gracefully', async () => {
    global.fetch = createMockFetchError('Network error')
    
    render(<EventsPage />)

    // Should show error message
    await waitFor(() => {
      expect(screen.getByText(/error loading events/i)).toBeInTheDocument()
    })

    // Should show retry button
    expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument()
  })

  it('allows expanding event details', async () => {
    global.fetch = createMockFetch(mockEvents)
    
    render(<EventsPage />)

    await waitFor(() => {
      expect(screen.getByText('React Workshop')).toBeInTheDocument()
    })

    // Find and click the expand button for React Workshop
    const expandButtons = screen.getAllByRole('button', { name: /toggle event details/i })
    const reactWorkshopButton = expandButtons[0]
    
    // Click to expand
    reactWorkshopButton.click()

    // Should show expanded content
    await waitFor(() => {
      expect(screen.getByText(/event details/i)).toBeInTheDocument()
    })

    expect(screen.getByText(/basic javascript knowledge/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /register now/i })).toBeInTheDocument()
  })

  it('shows registration button for upcoming events', async () => {
    global.fetch = createMockFetch(mockEvents)
    
    render(<EventsPage />)

    await waitFor(() => {
      expect(screen.getByText('React Workshop')).toBeInTheDocument()
    })

    // Expand the first event
    const expandButtons = screen.getAllByRole('button', { name: /toggle event details/i })
    expandButtons[0].click()

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /register now/i })).toBeInTheDocument()
    })

    const registerButton = screen.getByRole('button', { name: /register now/i })
    expect(registerButton.closest('a')).toHaveAttribute('href', mockEvents[0].registrationLink)
  })

  it('shows closed registration for past events', async () => {
    global.fetch = createMockFetch(mockEvents)
    
    render(<EventsPage />)

    await waitFor(() => {
      expect(screen.getByText('Networking Event')).toBeInTheDocument()
    })

    // Find past event and expand it
    const pastEventCards = screen.getByRole('heading', { name: /past events/i }).closest('section')
    const expandButton = pastEventCards?.querySelector('button[aria-expanded]')
    
    if (expandButton) {
      expandButton.click()

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /registrations closed/i })).toBeInTheDocument()
      })

      const closedButton = screen.getByRole('button', { name: /registrations closed/i })
      expect(closedButton).toBeDisabled()
    }
  })

  it('handles empty events list', async () => {
    global.fetch = createMockFetch([])
    
    render(<EventsPage />)

    await waitFor(() => {
      expect(screen.getByText(/no upcoming events at the moment/i)).toBeInTheDocument()
    })
  })

  it('navigates between multiple upcoming events', async () => {
    const multipleUpcomingEvents = mockEvents.filter(event => 
      new Date(event.date) > new Date()
    )
    
    global.fetch = createMockFetch(multipleUpcomingEvents)
    
    render(<EventsPage />)

    await waitFor(() => {
      expect(screen.getByText('React Workshop')).toBeInTheDocument()
    })

    // Should show navigation arrows if multiple events
    if (multipleUpcomingEvents.length > 1) {
      const nextButton = screen.getByRole('button', { name: /next event/i })
      const prevButton = screen.getByRole('button', { name: /previous event/i })
      
      expect(nextButton).toBeInTheDocument()
      expect(prevButton).toBeInTheDocument()
      
      // Click next
      nextButton.click()
      
      await waitFor(() => {
        expect(screen.getByText('Python Bootcamp')).toBeInTheDocument()
      })
    }
  })

  it('has proper accessibility structure', async () => {
    global.fetch = createMockFetch(mockEvents)
    
    render(<EventsPage />)

    await waitFor(() => {
      expect(screen.getByText('React Workshop')).toBeInTheDocument()
    })

    // Check for proper heading hierarchy
    expect(screen.getByRole('heading', { level: 2, name: /upcoming events/i })).toBeInTheDocument()
    expect(screen.getByRole('heading', { level: 2, name: /past events/i })).toBeInTheDocument()

    // Check for proper landmark regions
    const main = screen.getByRole('main')
    expect(main).toBeInTheDocument()

    // Check for proper button labels
    const expandButtons = screen.getAllByRole('button')
    expandButtons.forEach(button => {
      expect(button).toHaveAccessibleName()
    })
  })

  it('retries API call when retry button is clicked', async () => {
    // First call fails
    global.fetch = createMockFetchError('Network error')
    
    render(<EventsPage />)

    await waitFor(() => {
      expect(screen.getByText(/error loading events/i)).toBeInTheDocument()
    })

    // Mock successful retry
    global.fetch = createMockFetch(mockEvents)

    // Click retry
    const retryButton = screen.getByRole('button', { name: /try again/i })
    retryButton.click()

    // Should show events after retry
    await waitFor(() => {
      expect(screen.getByText('React Workshop')).toBeInTheDocument()
    })
  })
})
